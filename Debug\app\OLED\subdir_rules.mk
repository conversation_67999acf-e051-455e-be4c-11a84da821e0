################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
app/OLED/%.o: ../app/OLED/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/test1/bsp" -I"C:/Users/<USER>/workspace_ccstheia/test1/app" -I"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED" -I"C:/Users/<USER>/workspace_ccstheia/test1" -I"C:/Users/<USER>/workspace_ccstheia/test1/Debug" -I"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"C:/TI/mspm0_sdk_2_05_01_00/source" -I"C:/Users/<USER>/workspace_ccstheia/test1/bsp" -I"C:/Users/<USER>/workspace_ccstheia/test1/app" -gdwarf-3 -MMD -MP -MF"app/OLED/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


