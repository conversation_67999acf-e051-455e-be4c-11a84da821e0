[{"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/app/Ganway.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/app/No_Mcu_Ganv_Grayscale_Sensor.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/app/OLED/oled.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/app/Scheduler.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/app/encoder.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/app/key.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/app/motor.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/app/ringbuffer.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/bsp/bsp_usart.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/bsp/systick.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/test1/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/test1\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/Debug\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/TI/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"E:/TI/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/test1/empty.c"}]