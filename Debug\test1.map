******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 00:41:12 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002a01


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004c40  0001b3c0  R  X
  SRAM                  20200000   00008000  00000923  000076dd  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004c40   00004c40    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000030d8   000030d8    r-x .text
  00003198    00003198    00001a50   00001a50    r-- .rodata
  00004be8    00004be8    00000058   00000058    r-- .cinit
20200000    20200000    0000072a   00000000    rw-
  20200000    20200000    00000561   00000000    rw- .bss
  20200568    20200568    000001c2   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000030d8     
                  000000c0    00000580     Ganway.o (.text.Way)
                  00000640    000001d0     oled.o (.text.OLED_ShowChar)
                  00000810    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009a4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000b36    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000b38    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000cc0    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000de0    00000118     empty.o (.text.main)
                  00000ef8    0000010c     motor.o (.text.Set_PWM)
                  00001004    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001110    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001214    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000012fc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000013e0    000000e2     oled.o (.text.OLED_ShowNum)
                  000014c2    000000de     oled.o (.text.OLED_Init)
                  000015a0    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000167c    000000d8     empty.o (.text.TIMG0_IRQHandler)
                  00001754    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00001824    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000018ce    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001968    0000009a     oled.o (.text.OLED_ShowString)
                  00001a02    00000002     --HOLE-- [fill = 0]
                  00001a04    00000090     oled.o (.text.OLED_DrawPoint)
                  00001a94    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001b20    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001bac    00000084     oled.o (.text.OLED_Refresh)
                  00001c30    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001cb4    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001d30    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001da4    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001e16    00000002     --HOLE-- [fill = 0]
                  00001e18    0000006c     oled.o (.text.OLED_WR_Byte)
                  00001e84    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00001ef0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001f58    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001fba    00000002     --HOLE-- [fill = 0]
                  00001fbc    00000060     oled.o (.text.OLED_Clear)
                  0000201c    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000207a    00000002     --HOLE-- [fill = 0]
                  0000207c    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000020d4    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002128    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  00002178    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000021c8    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002214    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002260    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000022aa    00000002     --HOLE-- [fill = 0]
                  000022ac    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000022f6    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002340    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002388    00000048     oled.o (.text.OLED_DisplayTurn)
                  000023d0    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002418    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002460    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000024a4    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000024e6    00000002     --HOLE-- [fill = 0]
                  000024e8    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002528    00000040     key.o (.text.Key)
                  00002568    00000040     key.o (.text.Key_1)
                  000025a8    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  000025e8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002628    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002664    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000026a0    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  000026dc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002718    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00002752    00000002     --HOLE-- [fill = 0]
                  00002754    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00002788    00000034     oled.o (.text.OLED_ColorTurn)
                  000027bc    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000027f0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002824    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00002854    00000030     oled.o (.text.OLED_Pow)
                  00002884    00000030     systick.o (.text.SysTick_Handler)
                  000028b4    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000028e0    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  0000290c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00002938    00000028     empty.o (.text.DL_Common_updateReg)
                  00002960    00000028     oled.o (.text.DL_Common_updateReg)
                  00002988    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000029b0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000029d8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00002a00    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002a28    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00002a4e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00002a74    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00002a98    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002ab8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00002ad8    00000020     systick.o (.text.delay_ms)
                  00002af8    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00002b16    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002b34    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00002b50    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00002b6c    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002b88    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002ba4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00002bc0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002bdc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002bf8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002c14    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00002c30    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002c4c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002c68    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002c84    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002ca0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00002cb8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00002cd0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00002ce8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002d00    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002d18    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002d30    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002d48    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002d60    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00002d78    00000018     empty.o (.text.DL_GPIO_setPins)
                  00002d90    00000018     motor.o (.text.DL_GPIO_setPins)
                  00002da8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002dc0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00002dd8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00002df0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00002e08    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00002e20    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00002e38    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00002e50    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002e68    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002e80    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002e98    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002eb0    00000018     empty.o (.text.DL_Timer_startCounter)
                  00002ec8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002ee0    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002ef8    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00002f0e    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00002f24    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00002f3a    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002f50    00000016     key.o (.text.DL_GPIO_readPins)
                  00002f66    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002f7c    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00002f90    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00002fa4    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00002fb8    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002fcc    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00002fe0    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00002ff4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003008    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000301c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003030    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003044    00000014     motor.o (.text.Left_Control)
                  00003058    00000014     motor.o (.text.Right_Control)
                  0000306c    00000014     motor.o (.text.Right_Little_Control)
                  00003080    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00003092    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  000030a4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000030b6    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000030c8    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000030da    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  000030ea    00000002     --HOLE-- [fill = 0]
                  000030ec    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000030fc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000310c    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  0000311c    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  0000312a    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00003138    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003144    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003150    0000000c     systick.o (.text.get_systicks)
                  0000315c    0000000c     Scheduler.o (.text.scheduler_init)
                  00003168    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003172    00000002     --HOLE-- [fill = 0]
                  00003174    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000317c    00000006     libc.a : exit.c.obj (.text:abort)
                  00003182    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003186    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000318a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000318e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00003192    00000006     --HOLE-- [fill = 0]

.cinit     0    00004be8    00000058     
                  00004be8    0000002f     (.cinit..data.load) [load image, compression = lzss]
                  00004c17    00000001     --HOLE-- [fill = 0]
                  00004c18    0000000c     (__TI_handler_table)
                  00004c24    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004c2c    00000010     (__TI_cinit_table)
                  00004c3c    00000004     --HOLE-- [fill = 0]

.rodata    0    00003198    00001a50     
                  00003198    00000d5c     oled.o (.rodata.asc2_2412)
                  00003ef4    000005f0     oled.o (.rodata.asc2_1608)
                  000044e4    00000474     oled.o (.rodata.asc2_1206)
                  00004958    00000228     oled.o (.rodata.asc2_0806)
                  00004b80    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004ba8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00004bbc    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004bc6    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004bc8    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00004bd0    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00004bd8    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00004bdb    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00004bde    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00004be1    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004be3    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000561     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:Run)
                  20200550    00000004     (.common:encoderA_cnt)
                  20200554    00000004     (.common:encoderB_cnt)
                  20200558    00000004     (.common:gpio_interrup1)
                  2020055c    00000004     (.common:gpio_interrup2)
                  20200560    00000001     (.common:task_num)

.data      0    20200568    000001c2     UNINITIALIZED
                  20200568    00000100     empty.o (.data.rx_buff)
                  20200668    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e8    00000010     empty.o (.data.Anolog)
                  202006f8    00000010     empty.o (.data.black)
                  20200708    00000010     empty.o (.data.white)
                  20200718    00000008     systick.o (.data.systicks)
                  20200720    00000004     empty.o (.data.D_Num)
                  20200724    00000004     systick.o (.data.delay_times)
                  20200728    00000001     bsp_usart.o (.data.uart_rx_index)
                  20200729    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          786     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3566    291       516    
                                                                 
    .\app\
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       Ganway.o                         1408    0         0      
       encoder.o                        362     0         16     
       motor.o                          372     0         0      
       key.o                            150     0         0      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3718    0         17     
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    E:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              40      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           300     0         0      
                                                                 
    E:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    E:\TI\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1514    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       83        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     12480   7006      2339   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004c2c records: 2, size/record: 8, table size: 16
	.data: load addr=00004be8, load size=0000002f bytes, run addr=20200568, run size=000001c2 bytes, compression=lzss
	.bss: load addr=00004c24, load size=00000008 bytes, run addr=20200000, run size=00000561 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004c18 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003183  ADC0_IRQHandler                      
00003183  ADC1_IRQHandler                      
00003183  AES_IRQHandler                       
202006e8  Anolog                               
00003186  C$$EXIT                              
00003183  CANFD0_IRQHandler                    
00003183  DAC0_IRQHandler                      
000024e9  DL_ADC12_setClockConfig              
00003169  DL_Common_delayCycles                
0000201d  DL_I2C_fillControllerTXFIFO          
00002a4f  DL_I2C_setClockConfig                
000015a1  DL_SYSCTL_configSYSPLL               
00002461  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001111  DL_Timer_initFourCCPWMMode           
00001215  DL_Timer_initTimerMode               
00002c4d  DL_Timer_setCaptCompUpdateMethod     
00002e99  DL_Timer_setCaptureCompareOutCtl     
000030fd  DL_Timer_setCaptureCompareValue      
00002c69  DL_Timer_setClockConfig              
00002341  DL_UART_init                         
000030a5  DL_UART_setClockConfig               
00003183  DMA_IRQHandler                       
20200720  D_Num                                
00003183  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
00003183  GROUP0_IRQHandler                    
00000cc1  GROUP1_IRQHandler                    
00001755  Get_Analog_value                     
000026a1  Get_Anolog_Value                     
0000311d  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003187  HOSTexit                             
00003183  HardFault_Handler                    
00003183  I2C0_IRQHandler                      
00003183  I2C1_IRQHandler                      
00002529  Key                                  
00002569  Key_1                                
00003045  Left_Control                         
00003183  NMI_Handler                          
00000b39  No_MCU_Ganv_Sensor_Init              
00001da5  No_MCU_Ganv_Sensor_Init_Frist        
000024a5  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001fbd  OLED_Clear                           
00002789  OLED_ColorTurn                       
00002389  OLED_DisplayTurn                     
00001a05  OLED_DrawPoint                       
20200000  OLED_GRAM                            
000014c3  OLED_Init                            
00002855  OLED_Pow                             
00001bad  OLED_Refresh                         
00000641  OLED_ShowChar                        
000013e1  OLED_ShowNum                         
000018cf  OLED_ShowSignedNum                   
00001969  OLED_ShowString                      
00001e19  OLED_WR_Byte                         
00003183  PendSV_Handler                       
00003183  RTC_IRQHandler                       
0000318b  Reset_Handler                        
00003059  Right_Control                        
0000306d  Right_Little_Control                 
2020054c  Run                                  
00003183  SPI0_IRQHandler                      
00003183  SPI1_IRQHandler                      
00003183  SVC_Handler                          
000023d1  SYSCFG_DL_ADC12_0_init               
00000811  SYSCFG_DL_GPIO_init                  
0000207d  SYSCFG_DL_I2C_OLED_init              
00001a95  SYSCFG_DL_PWM_0_init                 
00002419  SYSCFG_DL_SYSCTL_init                
00003139  SYSCFG_DL_SYSTICK_init               
000027bd  SYSCFG_DL_TIMER_0_init               
000020d5  SYSCFG_DL_UART_0_init                
000027f1  SYSCFG_DL_init                       
00001b21  SYSCFG_DL_initPower                  
00000ef9  Set_PWM                              
00002885  SysTick_Handler                      
00003183  TIMA0_IRQHandler                     
00003183  TIMA1_IRQHandler                     
0000167d  TIMG0_IRQHandler                     
00003183  TIMG12_IRQHandler                    
00003183  TIMG6_IRQHandler                     
00003183  TIMG7_IRQHandler                     
00003183  TIMG8_IRQHandler                     
000030b7  TI_memcpy_small                      
0000312b  TI_memset_small                      
000025a9  UART0_IRQHandler                     
00003183  UART1_IRQHandler                     
00003183  UART2_IRQHandler                     
00003183  UART3_IRQHandler                     
000000c1  Way                                  
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004c2c  __TI_CINIT_Base                      
00004c3c  __TI_CINIT_Limit                     
00004c3c  __TI_CINIT_Warm                      
00004c18  __TI_Handler_Table_Base              
00004c24  __TI_Handler_Table_Limit             
000026dd  __TI_auto_init_nobinit_nopinit       
00001cb5  __TI_decompress_lzss                 
000030c9  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000310d  __TI_zero_init                       
000009af  __adddf3                             
000022ad  __aeabi_d2iz                         
000009af  __aeabi_dadd                         
00001f59  __aeabi_dcmpeq                       
00001f95  __aeabi_dcmpge                       
00001fa9  __aeabi_dcmpgt                       
00001f81  __aeabi_dcmple                       
00001f6d  __aeabi_dcmplt                       
00001005  __aeabi_ddiv                         
000012fd  __aeabi_dmul                         
000009a5  __aeabi_dsub                         
0000290d  __aeabi_i2d                          
00000b37  __aeabi_idiv0                        
00003145  __aeabi_memclr                       
00003145  __aeabi_memclr4                      
00003145  __aeabi_memclr8                      
00003175  __aeabi_memcpy                       
00003175  __aeabi_memcpy4                      
00003175  __aeabi_memcpy8                      
00002a75  __aeabi_ui2d                         
000025e9  __aeabi_uidiv                        
000025e9  __aeabi_uidivmod                     
ffffffff  __binit__                            
00001ef1  __cmpdf2                             
00001005  __divdf3                             
00001ef1  __eqdf2                              
000022ad  __fixdfsi                            
0000290d  __floatsidf                          
00002a75  __floatunsidf                        
00001d31  __gedf2                              
00001d31  __gtdf2                              
00001ef1  __ledf2                              
00001ef1  __ltdf2                              
UNDEFED   __mpu_init                           
000012fd  __muldf3                             
00002719  __muldsi3                            
00001ef1  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000009a5  __subdf3                             
00002a01  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
0000318f  _system_pre_init                     
0000317d  abort                                
000022f7  adc_getValue                         
00004958  asc2_0806                            
000044e4  asc2_1206                            
00003ef4  asc2_1608                            
00003198  asc2_2412                            
ffffffff  binit                                
202006f8  black                                
00001e85  convertAnalogToDigital               
00002ad9  delay_ms                             
20200724  delay_times                          
20200550  encoderA_cnt                         
20200554  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003151  get_systicks                         
20200558  gpio_interrup1                       
2020055c  gpio_interrup2                       
00000000  interruptVectors                     
00000de1  main                                 
00001825  normalizeAnalogValues                
20200568  rx_buff                              
0000315d  scheduler_init                       
20200560  task_num                             
20200668  uart_rx_buffer                       
20200728  uart_rx_index                        
20200729  uart_rx_ticks                        
20200708  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Way                                  
00000200  __STACK_SIZE                         
00000641  OLED_ShowChar                        
00000811  SYSCFG_DL_GPIO_init                  
000009a5  __aeabi_dsub                         
000009a5  __subdf3                             
000009af  __adddf3                             
000009af  __aeabi_dadd                         
00000b37  __aeabi_idiv0                        
00000b39  No_MCU_Ganv_Sensor_Init              
00000cc1  GROUP1_IRQHandler                    
00000de1  main                                 
00000ef9  Set_PWM                              
00001005  __aeabi_ddiv                         
00001005  __divdf3                             
00001111  DL_Timer_initFourCCPWMMode           
00001215  DL_Timer_initTimerMode               
000012fd  __aeabi_dmul                         
000012fd  __muldf3                             
000013e1  OLED_ShowNum                         
000014c3  OLED_Init                            
000015a1  DL_SYSCTL_configSYSPLL               
0000167d  TIMG0_IRQHandler                     
00001755  Get_Analog_value                     
00001825  normalizeAnalogValues                
000018cf  OLED_ShowSignedNum                   
00001969  OLED_ShowString                      
00001a05  OLED_DrawPoint                       
00001a95  SYSCFG_DL_PWM_0_init                 
00001b21  SYSCFG_DL_initPower                  
00001bad  OLED_Refresh                         
00001cb5  __TI_decompress_lzss                 
00001d31  __gedf2                              
00001d31  __gtdf2                              
00001da5  No_MCU_Ganv_Sensor_Init_Frist        
00001e19  OLED_WR_Byte                         
00001e85  convertAnalogToDigital               
00001ef1  __cmpdf2                             
00001ef1  __eqdf2                              
00001ef1  __ledf2                              
00001ef1  __ltdf2                              
00001ef1  __nedf2                              
00001f59  __aeabi_dcmpeq                       
00001f6d  __aeabi_dcmplt                       
00001f81  __aeabi_dcmple                       
00001f95  __aeabi_dcmpge                       
00001fa9  __aeabi_dcmpgt                       
00001fbd  OLED_Clear                           
0000201d  DL_I2C_fillControllerTXFIFO          
0000207d  SYSCFG_DL_I2C_OLED_init              
000020d5  SYSCFG_DL_UART_0_init                
000022ad  __aeabi_d2iz                         
000022ad  __fixdfsi                            
000022f7  adc_getValue                         
00002341  DL_UART_init                         
00002389  OLED_DisplayTurn                     
000023d1  SYSCFG_DL_ADC12_0_init               
00002419  SYSCFG_DL_SYSCTL_init                
00002461  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000024a5  No_Mcu_Ganv_Sensor_Task_Without_tick 
000024e9  DL_ADC12_setClockConfig              
00002529  Key                                  
00002569  Key_1                                
000025a9  UART0_IRQHandler                     
000025e9  __aeabi_uidiv                        
000025e9  __aeabi_uidivmod                     
000026a1  Get_Anolog_Value                     
000026dd  __TI_auto_init_nobinit_nopinit       
00002719  __muldsi3                            
00002789  OLED_ColorTurn                       
000027bd  SYSCFG_DL_TIMER_0_init               
000027f1  SYSCFG_DL_init                       
00002855  OLED_Pow                             
00002885  SysTick_Handler                      
0000290d  __aeabi_i2d                          
0000290d  __floatsidf                          
00002a01  _c_int00_noargs                      
00002a4f  DL_I2C_setClockConfig                
00002a75  __aeabi_ui2d                         
00002a75  __floatunsidf                        
00002ad9  delay_ms                             
00002c4d  DL_Timer_setCaptCompUpdateMethod     
00002c69  DL_Timer_setClockConfig              
00002e99  DL_Timer_setCaptureCompareOutCtl     
00003045  Left_Control                         
00003059  Right_Control                        
0000306d  Right_Little_Control                 
000030a5  DL_UART_setClockConfig               
000030b7  TI_memcpy_small                      
000030c9  __TI_decompress_none                 
000030fd  DL_Timer_setCaptureCompareValue      
0000310d  __TI_zero_init                       
0000311d  Get_Digtal_For_User                  
0000312b  TI_memset_small                      
00003139  SYSCFG_DL_SYSTICK_init               
00003145  __aeabi_memclr                       
00003145  __aeabi_memclr4                      
00003145  __aeabi_memclr8                      
00003151  get_systicks                         
0000315d  scheduler_init                       
00003169  DL_Common_delayCycles                
00003175  __aeabi_memcpy                       
00003175  __aeabi_memcpy4                      
00003175  __aeabi_memcpy8                      
0000317d  abort                                
00003183  ADC0_IRQHandler                      
00003183  ADC1_IRQHandler                      
00003183  AES_IRQHandler                       
00003183  CANFD0_IRQHandler                    
00003183  DAC0_IRQHandler                      
00003183  DMA_IRQHandler                       
00003183  Default_Handler                      
00003183  GROUP0_IRQHandler                    
00003183  HardFault_Handler                    
00003183  I2C0_IRQHandler                      
00003183  I2C1_IRQHandler                      
00003183  NMI_Handler                          
00003183  PendSV_Handler                       
00003183  RTC_IRQHandler                       
00003183  SPI0_IRQHandler                      
00003183  SPI1_IRQHandler                      
00003183  SVC_Handler                          
00003183  TIMA0_IRQHandler                     
00003183  TIMA1_IRQHandler                     
00003183  TIMG12_IRQHandler                    
00003183  TIMG6_IRQHandler                     
00003183  TIMG7_IRQHandler                     
00003183  TIMG8_IRQHandler                     
00003183  UART1_IRQHandler                     
00003183  UART2_IRQHandler                     
00003183  UART3_IRQHandler                     
00003186  C$$EXIT                              
00003187  HOSTexit                             
0000318b  Reset_Handler                        
0000318f  _system_pre_init                     
00003198  asc2_2412                            
00003ef4  asc2_1608                            
000044e4  asc2_1206                            
00004958  asc2_0806                            
00004c18  __TI_Handler_Table_Base              
00004c24  __TI_Handler_Table_Limit             
00004c2c  __TI_CINIT_Base                      
00004c3c  __TI_CINIT_Limit                     
00004c3c  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  Run                                  
20200550  encoderA_cnt                         
20200554  encoderB_cnt                         
20200558  gpio_interrup1                       
2020055c  gpio_interrup2                       
20200560  task_num                             
20200568  rx_buff                              
20200668  uart_rx_buffer                       
202006e8  Anolog                               
202006f8  black                                
20200708  white                                
20200720  D_Num                                
20200724  delay_times                          
20200728  uart_rx_index                        
20200729  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[189 symbols]
